<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue"
import * as fabric from 'fabric'
import { ElMessage } from "element-plus"
import { Loading } from "@element-plus/icons-vue"

defineOptions({
  name: "UltrasoundCanvas"
})

// 定义事件
const emit = defineEmits<{
  analyzeImage: [imageData: any]
}>()

// Canvas相关引用
const canvasRef = ref<HTMLCanvasElement>()
const canvasContainer = ref<HTMLDivElement>()
let fabricCanvas: fabric.Canvas | null = null

// 画布状态
const isLoading = ref(true)
const canvasReady = ref(false)
const showGrid = ref(true)
const zoomLevel = ref(1)
const minZoom = 0.1
const maxZoom = 5

// 工具栏状态
const toolbarVisible = ref(true)

// 图像列表状态
const imageList = ref<Array<{
  id: string
  name: string
  size: string
  format: string
  uploadTime: string
  thumbnail: string
  fabricObject?: any
}>>([])
const selectedImageId = ref<string>('')

// 画布配置
const canvasConfig = {
  backgroundColor: '#f5f5f5'
}

// 动态计算画布尺寸
const getCanvasSize = () => {
  if (!canvasContainer.value) {
    return { width: 1000, height: 700 }
  }

  const container = canvasContainer.value
  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight

  // 如果容器尺寸为0，等待布局完成
  if (containerWidth === 0 || containerHeight === 0) {
    return { width: 1000, height: 700 }
  }

  // 留出一些边距 - 减少padding以更好地利用空间
  const padding = 20
  const width = Math.max(400, containerWidth - padding)
  const height = Math.max(300, containerHeight - padding) // 适中的最小高度要求

  return { width, height }
}

// 网格配置
const gridConfig = {
  size: 20,
  color: '#ddd',
  strokeWidth: 1
}

// 等待容器尺寸就绪
const waitForContainerSize = async (maxRetries = 10): Promise<{ width: number, height: number }> => {
  for (let i = 0; i < maxRetries; i++) {
    const size = getCanvasSize()

    // 修改判断条件：确保容器有合理的尺寸
    if (size.width > 400 && size.height > 300) {
      return size
    }

    await new Promise(resolve => setTimeout(resolve, 200)) // 增加等待时间
  }

  return { width: 1000, height: 700 }
}

// 初始化画布
const initCanvas = async () => {
  if (!canvasRef.value || !canvasContainer.value) return

  try {
    isLoading.value = true

    // 等待容器尺寸就绪
    const { width, height } = await waitForContainerSize()

    // 创建Fabric.js画布实例
    fabricCanvas = new (fabric as any).Canvas(canvasRef.value, {
      width,
      height,
      backgroundColor: canvasConfig.backgroundColor,
      selection: true,
      preserveObjectStacking: true
    })

    // 绘制网格
    if (showGrid.value) {
      drawGrid()
    }

    // 添加事件监听
    addEventListeners()

    // 延迟更新画布尺寸，确保容器布局完成
    setTimeout(() => {
      updateCanvasSize()
    }, 100)

    // 再次延迟检查，确保所有布局都已完成
    setTimeout(() => {
      updateCanvasSize()
    }, 500)

    canvasReady.value = true
    ElMessage.success('画布加载完成')

  } catch (error) {
    ElMessage.error('画布初始化失败')
  } finally {
    isLoading.value = false
  }
}

// 绘制网格
const drawGrid = () => {
  if (!fabricCanvas) return

  // 清除现有网格
  const existingGrid = fabricCanvas.getObjects().filter((obj: any) => obj.name === 'grid-line')
  existingGrid.forEach((line: any) => fabricCanvas.remove(line))

  const { size, color, strokeWidth } = gridConfig

  // 获取画布实际尺寸
  const canvasWidth = fabricCanvas.getWidth()
  const canvasHeight = fabricCanvas.getHeight()

  // 简化网格绘制：直接基于画布尺寸，扩大范围确保完全覆盖
  const multiplier = 5 // 扩大倍数，确保覆盖足够大的范围
  const gridLeft = -canvasWidth * multiplier
  const gridTop = -canvasHeight * multiplier
  const gridRight = canvasWidth * multiplier
  const gridBottom = canvasHeight * multiplier

  // 计算网格起始位置（对齐到网格）
  const startX = Math.floor(gridLeft / size) * size
  const startY = Math.floor(gridTop / size) * size

  // 绘制垂直线
  for (let x = startX; x <= gridRight; x += size) {
    const line = new (fabric as any).Line([x, gridTop, x, gridBottom], {
      stroke: color,
      strokeWidth: strokeWidth,
      selectable: false,
      evented: false,
      name: 'grid-line',
      excludeFromExport: true
    })
    fabricCanvas.add(line)
  }

  // 绘制水平线
  for (let y = startY; y <= gridBottom; y += size) {
    const line = new (fabric as any).Line([gridLeft, y, gridRight, y], {
      stroke: color,
      strokeWidth: strokeWidth,
      selectable: false,
      evented: false,
      name: 'grid-line',
      excludeFromExport: true
    })
    fabricCanvas.add(line)
  }

  // 将网格移到最底层
  const gridLines = fabricCanvas.getObjects().filter((obj: any) => obj.name === 'grid-line')

  gridLines.forEach((line: any) => {
    // 尝试使用不同的API方法
    if (typeof fabricCanvas.sendToBack === 'function') {
      fabricCanvas.sendToBack(line)
    } else if (typeof fabricCanvas.sendObjectToBack === 'function') {
      fabricCanvas.sendObjectToBack(line)
    } else {
      // 使用索引方式移到底层
      const objects = fabricCanvas.getObjects()
      const index = objects.indexOf(line)
      if (index > 0) {
        fabricCanvas.moveTo(line, 0)
      }
    }
  })

  fabricCanvas.renderAll()
}

// 更新网格（在缩放或平移时调用）
let gridUpdateTimer: number | null = null
const updateGrid = () => {
  if (!showGrid.value) return

  // 防抖处理，避免频繁更新
  if (gridUpdateTimer) {
    clearTimeout(gridUpdateTimer)
  }

  gridUpdateTimer = setTimeout(() => {
    drawGrid()
    gridUpdateTimer = null
  }, 100) as unknown as number
}



// 添加事件监听
const addEventListeners = () => {
  if (!fabricCanvas) return
  
  // 鼠标滚轮缩放
  fabricCanvas.on('mouse:wheel', (opt) => {
    const delta = opt.e.deltaY
    let zoom = fabricCanvas!.getZoom()
    zoom *= 0.999 ** delta

    if (zoom > maxZoom) zoom = maxZoom
    if (zoom < minZoom) zoom = minZoom

    fabricCanvas!.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom)
    zoomLevel.value = Math.round(zoom * 100) / 100

    // 更新网格
    updateGrid()

    opt.e.preventDefault()
    opt.e.stopPropagation()
  })
  
  // 画布拖拽
  let isDragging = false
  let lastPosX = 0
  let lastPosY = 0

  fabricCanvas.on('mouse:down', (opt: any) => {
    const evt = opt.e
    if (evt.altKey === true) {
      isDragging = true
      fabricCanvas!.selection = false
      lastPosX = evt.clientX
      lastPosY = evt.clientY
    }
  })

  fabricCanvas.on('mouse:move', (opt: any) => {
    if (isDragging) {
      const e = opt.e
      const vpt = fabricCanvas!.viewportTransform!
      vpt[4] += e.clientX - lastPosX
      vpt[5] += e.clientY - lastPosY
      fabricCanvas!.requestRenderAll()
      lastPosX = e.clientX
      lastPosY = e.clientY
    }
  })



  fabricCanvas.on('mouse:up', () => {
    fabricCanvas!.setViewportTransform(fabricCanvas!.viewportTransform!)
    isDragging = false
    fabricCanvas!.selection = true
  })
}

// 缩放控制
const zoomIn = () => {
  if (!fabricCanvas) return
  let zoom = fabricCanvas.getZoom()
  zoom = zoom * 1.1
  if (zoom > maxZoom) zoom = maxZoom
  fabricCanvas.setZoom(zoom)
  zoomLevel.value = Math.round(zoom * 100) / 100
  updateGrid()
}

const zoomOut = () => {
  if (!fabricCanvas) return
  let zoom = fabricCanvas.getZoom()
  zoom = zoom / 1.1
  if (zoom < minZoom) zoom = minZoom
  fabricCanvas.setZoom(zoom)
  zoomLevel.value = Math.round(zoom * 100) / 100
  updateGrid()
}

const resetZoom = () => {
  if (!fabricCanvas) return
  fabricCanvas.setZoom(1)
  zoomLevel.value = 1
  updateGrid()
}

// 适应画布大小
const fitToCanvas = () => {
  if (!fabricCanvas || !canvasContainer.value) return

  // 重置缩放到100%
  fabricCanvas.setZoom(1)
  zoomLevel.value = 1
  updateGrid()
}

// 切换网格显示
const toggleGrid = () => {
  showGrid.value = !showGrid.value
  if (showGrid.value) {
    drawGrid()
  } else {
    // 移除网格
    const gridLines = fabricCanvas?.getObjects().filter(obj => obj.name === 'grid-line') || []
    gridLines.forEach(line => fabricCanvas?.remove(line))
    fabricCanvas?.renderAll()
  }
}

// 生成缩略图
const generateThumbnail = (imgElement: HTMLImageElement, maxSize = 100): string => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')!

  const { width, height } = imgElement
  const scale = Math.min(maxSize / width, maxSize / height)

  canvas.width = width * scale
  canvas.height = height * scale

  ctx.drawImage(imgElement, 0, 0, canvas.width, canvas.height)
  return canvas.toDataURL()
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 加载图像
const loadImage = (file: File) => {
  if (!fabricCanvas) {
    ElMessage.error('画布未初始化')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    const imgUrl = e.target?.result as string
    const img = new Image()
    img.onload = () => {
      const { width, height } = getCanvasSize()
      const fabricImg = new (fabric as any).Image(img, {
        left: width / 2,
        top: height / 2,
        originX: 'center',
        originY: 'center'
      })

      // 如果图像太大，缩放到合适大小
      const maxSize = Math.min(width, height) * 0.8
      if (fabricImg.width! > maxSize || fabricImg.height! > maxSize) {
        const scale = maxSize / Math.max(fabricImg.width!, fabricImg.height!)
        fabricImg.scale(scale)
      }

      // 生成唯一ID
      const imageId = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      fabricImg.set('imageId', imageId)

      fabricCanvas!.add(fabricImg)
      fabricCanvas!.setActiveObject(fabricImg)
      fabricCanvas!.renderAll()

      // 添加到图像列表
      const imageInfo = {
        id: imageId,
        name: file.name,
        size: formatFileSize(file.size),
        format: file.type.split('/')[1].toUpperCase(),
        uploadTime: new Date().toLocaleString('zh-CN'),
        thumbnail: generateThumbnail(img),
        fabricObject: fabricImg
      }

      imageList.value.push(imageInfo)
      selectedImageId.value = imageId

      ElMessage.success('图像加载成功')
    }
    img.onerror = () => {
      ElMessage.error('图像加载失败')
    }
    img.src = imgUrl
  }
  reader.readAsDataURL(file)
}

// 清空画布
const clearCanvas = () => {
  if (!fabricCanvas) return

  // 只清除非网格对象
  const objects = fabricCanvas.getObjects().filter((obj: any) => obj.name !== 'grid-line')
  objects.forEach((obj: any) => fabricCanvas!.remove(obj))
  fabricCanvas.renderAll()

  // 清空图像列表
  imageList.value = []
  selectedImageId.value = ''

  ElMessage.success('画布已清空')
}

// 选择图像
const selectImage = (imageId: string) => {
  selectedImageId.value = imageId
  const imageInfo = imageList.value.find(img => img.id === imageId)
  if (imageInfo && imageInfo.fabricObject && fabricCanvas) {
    fabricCanvas.setActiveObject(imageInfo.fabricObject)
    fabricCanvas.renderAll()
  }
}

// 删除图像
const deleteImage = (imageId: string) => {
  const imageInfo = imageList.value.find(img => img.id === imageId)
  if (imageInfo && imageInfo.fabricObject && fabricCanvas) {
    fabricCanvas.remove(imageInfo.fabricObject)
    fabricCanvas.renderAll()
  }

  imageList.value = imageList.value.filter(img => img.id !== imageId)

  if (selectedImageId.value === imageId) {
    selectedImageId.value = imageList.value.length > 0 ? imageList.value[0].id : ''
  }

  ElMessage.success('图像已删除')
}

// 显示/隐藏图像
const toggleImageVisibility = (imageId: string) => {
  const imageInfo = imageList.value.find(img => img.id === imageId)
  if (imageInfo && imageInfo.fabricObject) {
    const isVisible = imageInfo.fabricObject.visible
    imageInfo.fabricObject.set('visible', !isVisible)
    fabricCanvas?.renderAll()
  }
}

// 分析图像
const analyzeImage = (imageId: string) => {
  const imageInfo = imageList.value.find((img: any) => img.id === imageId)
  if (imageInfo) {
    try {
      // 准备传递的图像数据
      const imageData = {
        id: imageInfo.id,
        name: imageInfo.name,
        size: imageInfo.size,
        format: imageInfo.format,
        uploadTime: imageInfo.uploadTime,
        thumbnail: imageInfo.thumbnail
      }

      // 触发分析事件，传递给父组件
      emit('analyzeImage', imageData)

      ElMessage.success('正在切换到图像分析...')
    } catch (error) {
      console.error('分析失败:', error)
      ElMessage.error('分析失败，请重试')
    }
  }
}

// 加载示例图片
const loadSampleImage = () => {
  if (!fabricCanvas) {
    ElMessage.error('画布未初始化')
    return
  }

  // 创建一个简单的示例图片（超声图像模拟）
  const canvas = document.createElement('canvas')
  canvas.width = 400
  canvas.height = 300
  const ctx = canvas.getContext('2d')!

  // 绘制黑色背景
  ctx.fillStyle = '#000000'
  ctx.fillRect(0, 0, 400, 300)

  // 绘制一些模拟的超声图像内容
  ctx.fillStyle = '#333333'
  ctx.fillRect(50, 50, 300, 200)

  // 添加一些白色的模拟结构
  ctx.fillStyle = '#ffffff'
  ctx.beginPath()
  ctx.arc(200, 150, 30, 0, 2 * Math.PI)
  ctx.fill()

  ctx.fillStyle = '#cccccc'
  ctx.fillRect(100, 100, 200, 20)
  ctx.fillRect(120, 180, 160, 15)

  // 添加一些扫描线效果
  ctx.strokeStyle = '#666666'
  ctx.lineWidth = 1
  for (let i = 0; i < 400; i += 10) {
    ctx.beginPath()
    ctx.moveTo(i, 50)
    ctx.lineTo(i, 250)
    ctx.stroke()
  }

  // 转换为图片并添加到画布
  const dataURL = canvas.toDataURL()
  const img = new Image()
  img.onload = () => {
    const { width, height } = getCanvasSize()
    const fabricImg = new (fabric as any).Image(img, {
      left: width / 2,
      top: height / 2,
      originX: 'center',
      originY: 'center'
    })

    // 生成唯一ID
    const imageId = `sample_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    fabricImg.set('imageId', imageId)

    fabricCanvas!.add(fabricImg)
    fabricCanvas!.setActiveObject(fabricImg)
    fabricCanvas!.renderAll()

    // 添加到图像列表
    const imageInfo = {
      id: imageId,
      name: '超声图像_示例.jpg',
      size: '约 15 KB',
      format: 'JPG',
      uploadTime: new Date().toLocaleString('zh-CN'),
      thumbnail: generateThumbnail(img),
      fabricObject: fabricImg
    }

    imageList.value.push(imageInfo)
    selectedImageId.value = imageId

    ElMessage.success('示例图像加载成功')
  }
  img.src = dataURL
}



// 更新画布尺寸
const updateCanvasSize = () => {
  if (!fabricCanvas || !canvasContainer.value) return

  const { width, height } = getCanvasSize()
  const currentWidth = fabricCanvas.getWidth()
  const currentHeight = fabricCanvas.getHeight()

  // 只有尺寸真正改变时才更新
  if (Math.abs(currentWidth - width) > 5 || Math.abs(currentHeight - height) > 5) {
    fabricCanvas.setDimensions({ width, height })

    // 更新网格
    if (showGrid.value) {
      updateGrid()
    }

    fabricCanvas.renderAll()
  }
}

// 窗口大小变化处理
const handleResize = () => {
  nextTick(() => {
    updateCanvasSize()
  })
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  await initCanvas()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  if (fabricCanvas) {
    fabricCanvas.dispose()
    fabricCanvas = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div class="ultrasound-canvas">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <p>画布加载中...</p>
    </div>

    <!-- 工具栏 -->
    <div v-if="canvasReady && toolbarVisible" class="toolbar">
      <div class="toolbar-section">
        <el-button-group>
          <el-button :icon="'ZoomIn'" size="small" @click="zoomIn">
            放大
          </el-button>
          <el-button :icon="'ZoomOut'" size="small" @click="zoomOut">
            缩小
          </el-button>
          <el-button size="small" @click="resetZoom">
            重置 ({{ Math.round(zoomLevel * 100) }}%)
          </el-button>
          <el-button size="small" @click="fitToCanvas">
            适应
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-section">
        <el-button
          :type="showGrid ? 'primary' : 'default'"
          size="small"
          @click="toggleGrid"
        >
          {{ showGrid ? '隐藏网格' : '显示网格' }}
        </el-button>
      </div>

      <div class="toolbar-section">
        <el-upload
          :show-file-list="false"
          :before-upload="(file) => { loadImage(file); return false; }"
          accept="image/*"
        >
          <el-button :icon="'Upload'" size="small" type="primary">
            加载图像
          </el-button>
        </el-upload>

        <el-button :icon="'Delete'" size="small" @click="clearCanvas">
          清空
        </el-button>

        <el-button size="small" @click="loadSampleImage">
          加载示例
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 画布容器 -->
      <div
        ref="canvasContainer"
        class="canvas-container"
        :class="{ 'loading': isLoading }"
      >
        <canvas ref="canvasRef"></canvas>


      </div>

      <!-- 图像列表面板 -->
      <div class="image-panel">
        <div class="panel-header">
          <h4>图像信息</h4>
          <span class="image-count">{{ imageList.length }} 张图像</span>
        </div>

        <!-- 当前选中图像的详细信息 -->
        <div v-if="selectedImageId" class="image-details">
          <template v-for="image in imageList" :key="image.id">
            <div v-if="image.id === selectedImageId" class="detail-info">
              <div class="detail-item">
                <label>文件名：</label>
                <span>{{ image.name }}</span>
              </div>
              <div class="detail-item">
                <label>尺寸：</label>
                <span>{{ image.fabricObject?.width || 0 }} × {{ image.fabricObject?.height || 0 }}</span>
              </div>
              <div class="detail-item">
                <label>大小：</label>
                <span>{{ image.size }}</span>
              </div>
              <div class="detail-item">
                <label>格式：</label>
                <span>{{ image.format }}</span>
              </div>
              <div class="detail-item">
                <label>上传时间：</label>
                <span>{{ image.uploadTime }}</span>
              </div>
            </div>
          </template>
        </div>

        <!-- 图像列表 -->
        <div class="image-list">
          <h5>图像列表</h5>
          <div v-if="imageList.length === 0" class="empty-list">
            暂无图像
          </div>
          <div v-else class="list-items">
            <div
              v-for="image in imageList"
              :key="image.id"
              class="list-item"
              :class="{ 'active': image.id === selectedImageId }"
              @click="selectImage(image.id)"
            >
              <div class="item-thumbnail">
                <img :src="image.thumbnail" :alt="image.name" />
              </div>
              <div class="item-info">
                <div class="item-name">{{ image.name }}</div>
                <div class="item-meta">{{ image.size }}</div>
              </div>
              <div class="item-actions">
                <el-button
                  size="small"
                  :icon="'View'"
                  circle
                  @click.stop="toggleImageVisibility(image.id)"
                  :type="image.fabricObject?.visible !== false ? 'primary' : 'default'"
                />
                <el-button
                  size="small"
                  :icon="'DataAnalysis'"
                  circle
                  type="success"
                  @click.stop="analyzeImage(image.id)"
                  title="分析图像"
                />
                <el-button
                  size="small"
                  :icon="'Delete'"
                  circle
                  type="danger"
                  @click.stop="deleteImage(image.id)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ultrasound-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
  position: relative;

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    p {
      margin-top: 16px;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
  }

  .toolbar {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);
    flex-shrink: 0;
    flex-wrap: wrap;

    .toolbar-section {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    :deep(.el-button-group) {
      .el-button {
        border-radius: 4px;

        &:not(:first-child) {
          margin-left: -1px;
        }

        &:first-child {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }

        &:last-child {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }

        &:not(:first-child):not(:last-child) {
          border-radius: 0;
        }
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 16px;
    overflow: hidden;
  }

  .canvas-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: #fafafa;
    width: 100%;
    height: 100%;

    &.loading {
      pointer-events: none;
    }

    canvas {
      border: 1px solid var(--el-border-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      background: white;
      display: block;
    }
  }

  .image-panel {
    width: 300px;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .panel-header {
      padding: 16px;
      border-bottom: 1px solid var(--el-border-color-light);
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: var(--el-bg-color-page);

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .image-count {
        font-size: 12px;
        color: var(--el-text-color-regular);
        background: var(--el-color-primary-light-9);
        padding: 2px 8px;
        border-radius: 12px;
      }
    }

    .image-details {
      padding: 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      .detail-info {
        .detail-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 13px;

          &:last-child {
            margin-bottom: 0;
          }

          label {
            color: var(--el-text-color-regular);
            font-weight: 500;
            min-width: 60px;
          }

          span {
            color: var(--el-text-color-primary);
            text-align: right;
            word-break: break-all;
          }
        }
      }
    }

    .image-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      h5 {
        margin: 0;
        padding: 12px 16px 8px;
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .empty-list {
        padding: 32px 16px;
        text-align: center;
        color: var(--el-text-color-placeholder);
        font-size: 14px;
      }

      .list-items {
        flex: 1;
        overflow-y: auto;
        padding: 0 8px 8px;

        .list-item {
          display: flex;
          align-items: center;
          padding: 8px;
          margin-bottom: 4px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s;
          border: 1px solid transparent;

          &:hover {
            background: var(--el-color-primary-light-9);
          }

          &.active {
            background: var(--el-color-primary-light-8);
            border-color: var(--el-color-primary-light-5);
          }

          .item-thumbnail {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            overflow: hidden;
            margin-right: 8px;
            border: 1px solid var(--el-border-color-lighter);

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .item-info {
            flex: 1;
            min-width: 0;

            .item-name {
              font-size: 13px;
              color: var(--el-text-color-primary);
              margin-bottom: 2px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .item-meta {
              font-size: 11px;
              color: var(--el-text-color-regular);
            }
          }

          .item-actions {
            display: flex;
            gap: 2px;
            opacity: 0;
            transition: opacity 0.2s;

            :deep(.el-button) {
              padding: 4px;
              width: 22px;
              height: 22px;
              font-size: 12px;
            }
          }

          &:hover .item-actions {
            opacity: 1;
          }
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .ultrasound-canvas {
    .toolbar {
      padding: 8px 12px;
      gap: 12px;

      .toolbar-section {
        gap: 6px;
      }

      :deep(.el-button) {
        padding: 6px 12px;
        font-size: 12px;
      }
    }

    .main-content {
      flex-direction: column;
      gap: 12px;
    }



    .image-panel {
      width: 100%;
      height: 200px;

      .image-details {
        padding: 12px;

        .detail-item {
          font-size: 12px;
          margin-bottom: 6px;
        }
      }

      .image-list {
        .list-items {
          .list-item {
            padding: 6px;

            .item-thumbnail {
              width: 32px;
              height: 32px;
            }

            .item-info {
              .item-name {
                font-size: 12px;
              }

              .item-meta {
                font-size: 10px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
